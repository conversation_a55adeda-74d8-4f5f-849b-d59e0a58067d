import { Controller, Get, Param, Query, Inject } from '@midwayjs/core';
import { RelationshipService } from '../../service/relationship.service';

/**
 * 关系公开接口控制器
 */
@Controller('/public/relationship')
export class PublicRelationshipController {
  @Inject()
  relationshipService: RelationshipService;

  /**
   * 获取关系统计（公开）
   */
  @Get('/statistics')
  async getStatistics(@Query() filters?: any) {
    console.log('🔗 [公开] 获取关系统计');

    try {
      // 只返回启用状态的关联关系
      const statisticsFilters = { ...filters, status: 1 };
      const data = await this.relationshipService.getRelationshipStatistics(
        statisticsFilters
      );
      console.log('🔗 [公开] 关系统计获取成功');
      return data;
    } catch (error) {
      console.error('🔗 [公开] 关系统计获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取网络图数据（公开）
   */
  @Get('/network-graph')
  async getNetworkGraph(@Query() filters?: any) {
    console.log('🔗 [公开] 获取网络图数据');

    try {
      // 只返回启用状态的关联关系
      const graphFilters = { ...filters, status: 1 };
      const data = await this.relationshipService.getNetworkGraphData(
        graphFilters
      );
      console.log('🔗 [公开] 网络图数据获取成功:', {
        nodes: data.nodes.length,
        links: data.links.length,
        categories: data.categories.length,
      });
      return data;
    } catch (error) {
      console.error('🔗 [公开] 网络图数据获取失败:', error);
      throw error;
    }
  }

  /**
   * 根据要素获取关联关系（公开）
   */
  @Get('/by-element/:elementType/:elementId')
  async getByElement(
    @Param('elementType') elementType: string,
    @Param('elementId') elementId: number
  ) {
    console.log('🔗 [公开] 根据要素获取关联关系:', { elementType, elementId });

    try {
      const data = await this.relationshipService.getRelationsByElement(
        elementType,
        elementId
      );
      console.log('🔗 [公开] 要素关联关系获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 [公开] 要素关联关系获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系列表（公开，简化版）
   */
  @Get('/list')
  async getList(@Query() filters?: any) {
    console.log('🔗 [公开] 获取关系列表');

    try {
      // 只返回启用状态的关联关系，并限制返回字段
      const listFilters = { ...filters, status: 1 };
      const data = await this.relationshipService.getRelationshipList(
        listFilters
      );

      // 简化返回数据，只包含必要字段
      const simplifiedData = data.map(item => ({
        id: item.id,
        sourceType: item.sourceType,
        sourceElement: item.sourceElement,
        targetType: item.targetType,
        targetElement: item.targetElement,
        relationDict: item.relationDict,
        direction: item.direction,
        term: item.term,
        record: item.record,
      }));

      console.log('🔗 [公开] 关系列表获取成功:', simplifiedData.length);
      return simplifiedData;
    } catch (error) {
      console.error('🔗 [公开] 关系列表获取失败:', error);
      throw error;
    }
  }

  /**
   * 根据关系类型获取关联关系（公开）
   */
  @Get('/by-relation/:relationId')
  async getByRelation(@Param('relationId') relationId: number) {
    console.log('🔗 [公开] 根据关系类型获取关联关系:', relationId);

    try {
      const data = await this.relationshipService.getRelationshipList({
        relationDictId: relationId,
        status: 1,
      });
      console.log('🔗 [公开] 关系类型关联关系获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 [公开] 关系类型关联关系获取失败:', error);
      throw error;
    }
  }

  /**
   * 根据方向获取关联关系（公开）
   */
  @Get('/by-direction/:direction')
  async getByDirection(@Param('direction') direction: string) {
    console.log('🔗 [公开] 根据方向获取关联关系:', direction);

    try {
      const data = await this.relationshipService.getRelationshipList({
        direction: direction,
        status: 1,
      });
      console.log('🔗 [公开] 方向关联关系获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 [公开] 方向关联关系获取失败:', error);
      throw error;
    }
  }

  /**
   * 搜索关系（公开）
   */
  @Get('/search')
  async search(@Query('keyword') keyword: string, @Query() filters?: any) {
    console.log('🔗 [公开] 搜索关系:', keyword);

    try {
      if (!keyword || keyword.trim().length === 0) {
        throw new Error('搜索关键词不能为空');
      }

      const searchFilters = {
        ...filters,
        status: 1,
      };

      const data = await this.relationshipService.getRelationshipPage({
        keyword: keyword.trim(),
        page: 1,
        pageSize: 50, // 限制搜索结果数量
        ...searchFilters,
      });

      console.log('🔗 [公开] 关系搜索成功:', data.total);
      return data;
    } catch (error) {
      console.error('🔗 [公开] 关系搜索失败:', error);
      throw error;
    }
  }
}
