import { Rule, RuleType } from '@midwayjs/validate';
import { PageQueryDTO } from './common.dto';

/**
 * 创建关系DTO
 */
export class CreateRelationshipDTO {
  @Rule(RuleType.number().integer().optional())
  relationDictId?: number;

  @Rule(RuleType.number().integer().optional())
  parentRelationshipId?: number;

  @Rule(
    RuleType.string()
      .required()
      .valid('mountain', 'water_system', 'historical_element')
  )
  sourceType: string;

  @Rule(RuleType.number().integer().required())
  sourceId: number;

  @Rule(RuleType.string().required().valid('element', 'category'))
  targetType: string;

  @Rule(
    RuleType.string()
      .optional()
      .valid(
        'mountain',
        'water_system',
        'historical_element',
        'type_dict',
        'region_dict'
      )
  )
  targetEntityType?: string;

  @Rule(RuleType.number().integer().required())
  targetId: number;

  @Rule(RuleType.string().optional().max(50))
  direction?: string;

  @Rule(RuleType.string().optional().max(255))
  term?: string;

  @Rule(RuleType.string().optional())
  record?: string;

  @Rule(RuleType.number().integer().optional())
  sort?: number;

  @Rule(RuleType.number().integer().valid(0, 1).default(1))
  status?: number = 1;
}

/**
 * 更新关系DTO
 */
export class UpdateRelationshipDTO {
  @Rule(RuleType.number().integer().optional())
  relationDictId?: number;

  @Rule(RuleType.number().integer().optional())
  parentRelationshipId?: number;

  @Rule(
    RuleType.string()
      .optional()
      .valid('mountain', 'water_system', 'historical_element')
  )
  sourceType?: string;

  @Rule(RuleType.number().integer().optional())
  sourceId?: number;

  @Rule(RuleType.string().optional().valid('element', 'category'))
  targetType?: string;

  @Rule(
    RuleType.string()
      .optional()
      .valid(
        'mountain',
        'water_system',
        'historical_element',
        'type_dict',
        'region_dict'
      )
  )
  targetEntityType?: string;

  @Rule(RuleType.number().integer().optional())
  targetId?: number;

  @Rule(RuleType.string().optional().max(50))
  direction?: string;

  @Rule(RuleType.string().optional().max(255))
  term?: string;

  @Rule(RuleType.string().optional())
  record?: string;

  @Rule(RuleType.number().integer().optional())
  sort?: number;

  @Rule(RuleType.number().integer().valid(0, 1).optional())
  status?: number;
}

/**
 * 关系查询DTO
 */
export class RelationshipQueryDTO extends PageQueryDTO {
  @Rule(RuleType.string().optional())
  keyword?: string;

  @Rule(RuleType.number().integer().optional())
  relationDictId?: number;

  @Rule(RuleType.number().integer().optional())
  parentRelationshipId?: number;

  @Rule(
    RuleType.string()
      .optional()
      .valid('mountain', 'water_system', 'historical_element')
  )
  sourceType?: string;

  @Rule(RuleType.number().integer().optional())
  sourceId?: number;

  @Rule(RuleType.string().optional().valid('element', 'category'))
  targetType?: string;

  @Rule(
    RuleType.string()
      .optional()
      .valid(
        'mountain',
        'water_system',
        'historical_element',
        'type_dict',
        'region_dict'
      )
  )
  targetEntityType?: string;

  @Rule(RuleType.number().integer().optional())
  targetId?: number;

  @Rule(RuleType.string().optional())
  direction?: string;

  @Rule(RuleType.number().integer().valid(0, 1).optional())
  status?: number;
}

/**
 * 关系响应DTO
 */
export class RelationshipResponseDTO {
  id: number;
  relationDictId?: number;
  parentRelationshipId?: number;
  sourceType: string;
  sourceId: number;
  targetType: string;
  targetEntityType?: string;
  targetId: number;
  direction?: string;
  term?: string;
  record?: string;
  sort?: number;
  status: number;
  createdAt?: Date;
  updatedAt?: Date;

  // 关联数据
  relationDict?: any;
  parent?: any;
  children?: any[];
  sourceElement?: any;
  targetElement?: any;

  constructor(data: any) {
    this.id = data.id;
    this.relationDictId = data.relationDictId;
    this.parentRelationshipId = data.parentRelationshipId;
    this.sourceType = data.sourceType;
    this.sourceId = data.sourceId;
    this.targetType = data.targetType;
    this.targetEntityType = data.targetEntityType;
    this.targetId = data.targetId;
    this.direction = data.direction;
    this.term = data.term;
    this.record = data.record;
    this.sort = data.sort;
    this.status = data.status;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.relationDict = data.relationDict;
    this.parent = data.parent;
    this.children = data.children;
    this.sourceElement = data.sourceElement;
    this.targetElement = data.targetElement;
  }
}

/**
 * 批量创建关系DTO
 */
export class BatchCreateRelationshipDTO {
  @Rule(RuleType.array().items(RuleType.object()).required())
  relations: CreateRelationshipDTO[];
}

/**
 * 网络图节点DTO
 */
export class NetworkNodeDTO {
  id: string;
  name: string;
  type: string;
  category: string;
  size?: number;
  color?: string;
  x?: number;
  y?: number;
}

/**
 * 网络图连线DTO
 */
export class NetworkLinkDTO {
  source: string;
  target: string;
  relation: string;
  direction?: string;
  term?: string;
  weight?: number;
  color?: string;
}

/**
 * 网络图数据DTO
 */
export class NetworkGraphDTO {
  nodes: NetworkNodeDTO[];
  links: NetworkLinkDTO[];
  categories: string[];
}

/**
 * 关系统计DTO
 */
export class RelationshipStatisticsDTO {
  total: number;
  bySourceType: Array<{
    sourceType: string;
    count: number;
  }>;
  byTargetType: Array<{
    targetType: string;
    count: number;
  }>;
  byRelationType: Array<{
    relationId: number;
    relationName: string;
    count: number;
  }>;
  byDirection: Array<{
    direction: string;
    count: number;
  }>;
}
