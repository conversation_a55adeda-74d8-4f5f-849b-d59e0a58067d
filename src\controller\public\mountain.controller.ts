import { Controller, Get, Inject } from '@midwayjs/core';
import { MountainService } from '../../service/mountain.service';

/**
 * 山塬公开接口控制器
 */
@Controller('/api/mountain')
export class PublicMountainController {
  @Inject()
  mountainService: MountainService;

  /**
   * 获取所有山塬（不分页）
   */
  @Get('/all')
  async getAll() {
    console.log('🏔️ [公开] 获取所有山塬');

    try {
      const result = await this.mountainService.findAll({
        query: {}, // 查询所有
        order: [['code', 'ASC']], // 按编号排序
      });

      // 只返回必要字段
      const simplifiedData = result.list.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
      }));

      console.log('🏔️ [公开] 所有山塬获取成功:', simplifiedData.length);
      return simplifiedData;
    } catch (error) {
      console.error('🏔️ [公开] 所有山塬获取失败:', error);
      throw error;
    }
  }
}
