import { Controller, Get, Query, Inject, Param } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { MountainService } from '../../service/mountain.service';
import { WaterSystemService } from '../../service/water-system.service';
import { HistoricalElementService } from '../../service/historical-element.service';
import { PageQueryDTO } from '../../dto/common.dto';

/**
 * 数据查询公开接口
 */
@Controller('/public/data')
export class PublicDataController {
  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 获取山塬列表
   */
  @Get('/mountain')
  @Validate()
  async getMountainList(@Query() query: PageQueryDTO & { regionId?: number }) {
    const data = await this.mountainService.findList(query);
    return data;
  }

  /**
   * 获取山塬详情
   */
  @Get('/mountain/:id')
  async getMountainDetail(@Param('id') id: number) {
    // 验证ID参数
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的山塬ID');
    }
    const data = await this.mountainService.findById(Number(id));
    return data;
  }

  /**
   * 获取水系列表
   */
  @Get('/water-system')
  @Validate()
  async getWaterSystemList(
    @Query() query: PageQueryDTO & { regionId?: number }
  ) {
    const data = await this.waterSystemService.findList(query);
    return data;
  }

  /**
   * 获取水系详情
   */
  @Get('/water-system/:id')
  async getWaterSystemDetail(@Param('id') id: number) {
    // 验证ID参数
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的水系ID');
    }
    const data = await this.waterSystemService.findById(Number(id));
    return data;
  }

  /**
   * 获取历史要素列表
   */
  @Get('/historical-element')
  @Validate()
  async getHistoricalElementList(
    @Query() query: PageQueryDTO & { typeId?: number }
  ) {
    const data = await this.historicalElementService.findList(query);
    return data;
  }

  /**
   * 获取历史要素详情
   */
  @Get('/historical-element/:id')
  async getHistoricalElementDetail(@Param('id') id: number) {
    // 验证ID参数
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的历史要素ID');
    }
    const data = await this.historicalElementService.findById(Number(id));
    return data;
  }

  /**
   * 根据区域获取山塬
   */
  @Get('/mountain/by-region/:regionId')
  async getMountainsByRegion(@Param('regionId') regionId: number) {
    // 验证regionId参数
    if (!regionId || isNaN(Number(regionId))) {
      throw new Error('无效的区域ID');
    }
    const data = await this.mountainService.findByRegion(Number(regionId));
    return data;
  }

  /**
   * 根据区域获取水系
   */
  @Get('/water-system/by-region/:regionId')
  async getWaterSystemsByRegion(@Param('regionId') regionId: number) {
    // 验证regionId参数
    if (!regionId || isNaN(Number(regionId))) {
      throw new Error('无效的区域ID');
    }
    const data = await this.waterSystemService.findByRegion(Number(regionId));
    return data;
  }

  /**
   * 根据类型获取历史要素
   */
  @Get('/historical-element/by-type/:typeId')
  async getHistoricalElementsByType(@Param('typeId') typeId: number) {
    const data = await this.historicalElementService.findByType(typeId);
    return data;
  }

  /**
   * 根据区域获取历史要素
   */
  @Get('/historical-element/by-region/:regionId')
  async getHistoricalElementsByRegion(@Param('regionId') regionId: number) {
    const data = await this.historicalElementService.findByRegion(regionId);
    return data;
  }
}
