import { Controller, Get, Inject } from '@midwayjs/core';
import { HistoricalElementService } from '../../service/historical-element.service';

/**
 * 历史要素公开接口控制器
 */
@Controller('/api/historical-element')
export class PublicHistoricalElementController {
  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 获取所有历史要素（不分页）
   */
  @Get('/all')
  async getAll() {
    console.log('🏛️ [公开] 获取所有历史要素');

    try {
      const result = await this.historicalElementService.findAll({
        query: {}, // 查询所有
        order: [['code', 'ASC']], // 按编号排序
      });

      // 只返回必要字段
      const simplifiedData = result.list.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
      }));

      console.log('🏛️ [公开] 所有历史要素获取成功:', simplifiedData.length);
      return simplifiedData;
    } catch (error) {
      console.error('🏛️ [公开] 所有历史要素获取失败:', error);
      throw error;
    }
  }
}
