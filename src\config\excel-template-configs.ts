/**
 * Excel模板配置管理
 * 专门管理各种Excel模板的配置信息
 */

export interface ExcelTemplateFieldConfig {
  /** 字段标签 */
  label: string;
  /** 字段键名 */
  key: string;
  /** 是否必填 */
  required: boolean;
  /** 字段描述 */
  description?: string;
  /** 列宽 */
  width?: number;
  /** 数据类型 */
  type?: 'string' | 'number' | 'date' | 'boolean';
  /** 验证规则 */
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    options?: string[];
  };
}

export interface ExcelTemplateConfig {
  /** 模板标题 */
  title: string;
  /** 填写说明 */
  instructions: string[];
  /** 表头配置 */
  headers: ExcelTemplateFieldConfig[];
  /** 示例数据 */
  exampleData?: any[];
  /** 工作表名称 */
  sheetName?: string;
  /** 模板样式配置 */
  styleConfig?: {
    /** 是否启用样式 */
    enabled: boolean;
    /** 主题色 */
    theme?: 'blue' | 'green' | 'orange' | 'red';
    /** 是否显示边框 */
    showBorders?: boolean;
    /** 是否高亮必填字段 */
    highlightRequired?: boolean;
  };
}

/**
 * 历史要素字段配置
 */
export const HISTORICAL_ELEMENT_FIELDS: ExcelTemplateFieldConfig[] = [
  {
    label: '名称',
    key: 'name',
    required: true,
    description: '历史要素名称，不能为空',
    width: 25,
    type: 'string',
    validation: { max: 255 },
  },
  {
    label: '编号',
    key: 'code',
    required: true,
    description: '唯一编号，不能重复',
    width: 20,
    type: 'string',
    validation: { max: 50 },
  },
  {
    label: '类型名称',
    key: 'typeName',
    required: false,
    description: '历史要素类型名称，可选，需要在类型字典中存在',
    width: 15,
    type: 'string',
  },
  {
    label: '建筑经度',
    key: 'constructionLongitude',
    required: false,
    description: '建筑经度坐标，范围-180到180',
    width: 18,
    type: 'number',
    validation: { min: -180, max: 180 },
  },
  {
    label: '建筑纬度',
    key: 'constructionLatitude',
    required: false,
    description: '建筑纬度坐标，范围-90到90',
    width: 18,
    type: 'number',
    validation: { min: -90, max: 90 },
  },
  {
    label: '位置描述',
    key: 'locationDescription',
    required: false,
    description: '详细位置描述，可选',
    width: 30,
    type: 'string',
    validation: { max: 500 },
  },
  {
    label: '建造时间',
    key: 'constructionTime',
    required: false,
    description: '建造时间，格式：YYYY-MM-DD',
    width: 20,
    type: 'date',
  },
  {
    label: '历史记载',
    key: 'historicalRecords',
    required: false,
    description: '相关历史记载，可选',
    width: 40,
    type: 'string',
    validation: { max: 2000 },
  },
  {
    label: '区域名称',
    key: 'regionName',
    required: true,
    description: '所属区域名称，必填，需要在区域字典中存在',
    width: 15,
    type: 'string',
  },
];

/**
 * 历史要素Excel模板配置
 */
export const HISTORICAL_ELEMENT_TEMPLATE_CONFIG: ExcelTemplateConfig = {
  title: '历史要素导入模板',
  instructions: [
    '请按照以下要求填写历史要素数据',
    '带*号的字段为必填项，不能为空',
    '请严格按照示例格式填写数据',
    '填写完成后保存并上传文件',
  ],
  headers: HISTORICAL_ELEMENT_FIELDS,
  exampleData: [], // 暂时为空，避免内存问题
  sheetName: '历史要素数据',
  styleConfig: {
    enabled: true,
    theme: 'blue',
    showBorders: true,
    highlightRequired: true,
  },
};

/**
 * 山塬字段配置
 */
export const MOUNTAIN_FIELDS: ExcelTemplateFieldConfig[] = [
  {
    label: '名称',
    key: 'name',
    required: true,
    description: '山塬名称，不能为空',
    width: 25,
    type: 'string',
    validation: { max: 255 },
  },
  {
    label: '编号',
    key: 'code',
    required: true,
    description: '唯一编号，不能重复',
    width: 20,
    type: 'string',
    validation: { max: 50 },
  },
  {
    label: '山塬经度',
    key: 'longitude',
    required: false,
    description: '山塬经度坐标，范围-180到180',
    width: 18,
    type: 'number',
    validation: { min: -180, max: 180 },
  },
  {
    label: '山塬纬度',
    key: 'latitude',
    required: false,
    description: '山塬纬度坐标，范围-90到90',
    width: 18,
    type: 'number',
    validation: { min: -90, max: 90 },
  },
  {
    label: '高度',
    key: 'height',
    required: false,
    description: '山塬高度，单位：米，可选',
    width: 15,
    type: 'number',
    validation: { min: 0 },
  },
  {
    label: '历史记载',
    key: 'historicalRecords',
    required: false,
    description: '相关历史记载，可选',
    width: 40,
    type: 'string',
    validation: { max: 2000 },
  },
  {
    label: '区域名称',
    key: 'regionName',
    required: true,
    description: '所属区域名称，必填，需要在区域字典中存在',
    width: 15,
    type: 'string',
  },
];

/**
 * 山塬Excel模板配置
 */
export const MOUNTAIN_TEMPLATE_CONFIG: ExcelTemplateConfig = {
  title: '山塬导入模板',
  instructions: [
    '请按照以下要求填写山塬数据',
    '带*号的字段为必填项，不能为空',
    '经纬度字段为可选项',
    '填写完成后保存并上传文件',
  ],
  headers: MOUNTAIN_FIELDS,
  exampleData: [],
  sheetName: '山塬数据',
  styleConfig: {
    enabled: true,
    theme: 'green',
    showBorders: true,
    highlightRequired: true,
  },
};

/**
 * 水系字段配置
 */
export const WATER_SYSTEM_FIELDS: ExcelTemplateFieldConfig[] = [
  {
    label: '名称',
    key: 'name',
    required: true,
    description: '水系名称，不能为空',
    width: 25,
    type: 'string',
    validation: { max: 255 },
  },
  {
    label: '编号',
    key: 'code',
    required: true,
    description: '唯一编号，不能重复',
    width: 20,
    type: 'string',
    validation: { max: 50 },
  },
  {
    label: '水系经度',
    key: 'longitude',
    required: false,
    description: '水系经度坐标，范围-180到180',
    width: 18,
    type: 'number',
    validation: { min: -180, max: 180 },
  },
  {
    label: '水系纬度',
    key: 'latitude',
    required: false,
    description: '水系纬度坐标，范围-90到90',
    width: 18,
    type: 'number',
    validation: { min: -90, max: 90 },
  },
  {
    label: '长度面积',
    key: 'lengthArea',
    required: false,
    description: '水系长度或面积描述，可选',
    width: 20,
    type: 'string',
    validation: { max: 50 },
  },
  {
    label: '历史记载',
    key: 'historicalRecords',
    required: false,
    description: '相关历史记载，可选',
    width: 40,
    type: 'string',
    validation: { max: 2000 },
  },
  {
    label: '区域名称',
    key: 'regionName',
    required: true,
    description: '所属区域名称，必填，需要在区域字典中存在',
    width: 15,
    type: 'string',
  },
];

/**
 * 水系Excel模板配置
 */
export const WATER_SYSTEM_TEMPLATE_CONFIG: ExcelTemplateConfig = {
  title: '水系导入模板',
  instructions: [
    '请按照以下要求填写水系数据',
    '带*号的字段为必填项，不能为空',
    '经纬度字段为可选项',
    '填写完成后保存并上传文件',
  ],
  headers: WATER_SYSTEM_FIELDS,
  exampleData: [],
  sheetName: '水系数据',
  styleConfig: {
    enabled: true,
    theme: 'blue',
    showBorders: true,
    highlightRequired: true,
  },
};

/**
 * 所有模板配置的映射
 */
export const TEMPLATE_CONFIGS = {
  HISTORICAL_ELEMENT: HISTORICAL_ELEMENT_TEMPLATE_CONFIG,
  MOUNTAIN: MOUNTAIN_TEMPLATE_CONFIG,
  WATER_SYSTEM: WATER_SYSTEM_TEMPLATE_CONFIG,
} as const;

export type TemplateType = keyof typeof TEMPLATE_CONFIGS;
