import { Controller, Get, Inject } from '@midwayjs/core';
import { WaterSystemService } from '../../service/water-system.service';

/**
 * 水系公开接口控制器
 */
@Controller('/api/water-system')
export class PublicWaterSystemController {
  @Inject()
  waterSystemService: WaterSystemService;

  /**
   * 获取所有水系（不分页）
   */
  @Get('/all')
  async getAll() {
    console.log('🌊 [公开] 获取所有水系');

    try {
      const result = await this.waterSystemService.findAll({
        query: {}, // 查询所有
        order: [['code', 'ASC']], // 按编号排序
      });

      // 只返回必要字段
      const simplifiedData = result.list.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
      }));

      console.log('🌊 [公开] 所有水系获取成功:', simplifiedData.length);
      return simplifiedData;
    } catch (error) {
      console.error('🌊 [公开] 所有水系获取失败:', error);
      throw error;
    }
  }
}
