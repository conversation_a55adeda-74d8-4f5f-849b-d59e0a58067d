import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
  Files,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { MountainService } from '../../service/mountain.service';
import { ExcelService } from '../../service/excel.service';
import { PageQueryDTO } from '../../dto/common.dto';
import { CreateMountainDTO, UpdateMountainDTO } from '../../dto/entity.dto';
import { promises as fs } from 'fs';
import { Context as KoaContext } from '@midwayjs/koa';

/**
 * 山塬管理控制器
 */
@Controller('/admin/mountain', { middleware: [JwtMiddleware, AuthMiddleware] })
export class AdminMountainController {
  @Inject()
  mountainService: MountainService;

  @Inject()
  excelService: ExcelService;

  @Inject()
  ctx: KoaContext;

  /**
   * 创建山塬
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateMountainDTO) {
    const data = await this.mountainService.createMountain(createDto);
    return data;
  }

  /**
   * 更新山塬
   */
  @Put('/:id')
  @Validate()
  async update(@Param('id') id: number, @Body() updateDto: UpdateMountainDTO) {
    const data = await this.mountainService.updateMountain(id, updateDto);
    return data;
  }

  /**
   * 删除山塬
   */
  @Del('/:id')
  async delete(
    @Param('id') id: number,
    @Query('deletePhotos') deletePhotos?: string
  ) {
    // 将字符串参数转换为布尔值，默认为false（保留照片，外键设置为NULL）
    const shouldDeletePhotos = deletePhotos === 'true';
    await this.mountainService.deleteMountain(id, shouldDeletePhotos);
    return {
      message: '删除成功',
      deletedPhotos: shouldDeletePhotos,
    };
  }

  /**
   * 获取山塬列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() query: PageQueryDTO & { regionId?: number }) {
    const data = await this.mountainService.findList(query);
    return data;
  }

  /**
   * 获取山塬详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    // 验证ID参数
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的山塬ID');
    }
    const data = await this.mountainService.findById(Number(id));
    if (!data) {
      throw new Error('山塬不存在');
    }
    return data;
  }

  /**
   * 批量导入山塬
   */
  @Post('/batch-import')
  @Validate()
  async batchImport(@Body() data: { mountains: CreateMountainDTO[] }) {
    await this.mountainService.batchImportMountains(data.mountains);
    return { message: '批量导入成功' };
  }

  /**
   * 获取山塬统计
   */
  @Get('/statistics/overview')
  async getStatistics(@Query('regionId') regionId?: number) {
    const data = await this.mountainService.getStatistics(regionId);
    return data;
  }

  /**
   * 根据区域获取山塬
   */
  @Get('/by-region/:regionId')
  async getByRegion(@Param('regionId') regionId: number) {
    // 验证regionId参数
    if (!regionId || isNaN(Number(regionId))) {
      throw new Error('无效的区域ID');
    }
    const data = await this.mountainService.findByRegion(Number(regionId));
    return data;
  }

  /**
   * 根据高度范围查询
   */
  @Get('/by-height-range')
  async getByHeightRange(
    @Query('minHeight') minHeight?: number,
    @Query('maxHeight') maxHeight?: number
  ) {
    const data = await this.mountainService.findByHeightRange(
      minHeight,
      maxHeight
    );
    return data;
  }

  /**
   * 导出山塬数据到Excel
   */
  @Get('/export/excel')
  async exportToExcel(@Query('regionId') regionId?: number) {
    const whereConditions: any = {};
    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.mountainService.findAll({
      query: whereConditions,
    });

    const excelBuffer = await this.excelService.exportMountainToExcel(
      result.list
    );

    this.ctx.set(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    this.ctx.set('Content-Disposition', 'attachment; filename=mountains.xlsx');
    return excelBuffer;
  }

  /**
   * 下载山塬导入模板
   */
  @Get('/template/download')
  async downloadTemplate() {
    // 确保模板文件存在
    const templatePath = await this.excelService.getMountainImportTemplate();

    // 构建完整的下载URL
    const protocol = this.ctx.protocol;
    const host = this.ctx.host;
    const downloadUrl = `${protocol}://${host}${templatePath}`;

    return {
      downloadUrl,
      filename: '山塬导入模板.xlsx',
      description: '点击链接下载Excel导入模板，包含字段说明和示例数据',
    };
  }

  /**
   * 批量导入山塬数据
   */
  @Post('/import/execute', { middleware: [UploadMiddleware] })
  @Validate()
  async executeImport(@Files() files: UploadFileInfo[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要导入的Excel文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.isExcelFile(file.filename)) {
      throw new Error('请上传Excel文件（.xlsx或.xls格式）');
    }

    // 验证文件大小（限制为10MB）
    const fileSize = await this.getFileSize(file);
    if (fileSize > 10 * 1024 * 1024) {
      throw new Error('文件大小不能超过10MB');
    }

    try {
      // 解析Excel文件
      const parseResult = await this.excelService.parseMountainExcel(
        file.data as string
      );

      if (!parseResult.success || !parseResult.data) {
        throw new Error('文件解析失败或数据为空');
      }

      // 批量导入数据
      await this.mountainService.batchImportMountains(parseResult.data);

      return {
        success: true,
        message: '导入成功',
        totalRows: parseResult.totalRows,
        validRows: parseResult.validRows,
        importedCount: parseResult.data.length,
      };
    } catch (error) {
      throw new Error(`导入失败: ${error.message}`);
    } finally {
      // 清理临时文件
      try {
        if (typeof file.data === 'string') {
          await fs.unlink(file.data);
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }

  /**
   * 验证Excel文件预览（不实际导入）
   */
  @Post('/import/preview', { middleware: [UploadMiddleware] })
  @Validate()
  async previewImport(@Files() files: UploadFileInfo[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要上传的Excel文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.isExcelFile(file.filename)) {
      throw new Error('请上传Excel文件（.xlsx或.xls格式）');
    }

    try {
      // 解析Excel文件
      const parseResult = await this.excelService.parseMountainExcel(
        file.data as string
      );

      return {
        success: parseResult.success,
        message: parseResult.success ? '文件格式正确' : '文件存在错误',
        errors: parseResult.errors,
        totalRows: parseResult.totalRows || 0,
        validRows: parseResult.validRows || 0,
        preview: parseResult.data?.slice(0, 5), // 只返回前5条数据作为预览
      };
    } catch (error) {
      return {
        success: false,
        message: `文件解析失败: ${error.message}`,
        totalRows: 0,
        validRows: 0,
      };
    } finally {
      // 清理临时文件
      try {
        if (typeof file.data === 'string') {
          await fs.unlink(file.data);
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }

  /**
   * 验证是否为Excel文件
   */
  private isExcelFile(filename: string): boolean {
    const extension = filename.toLowerCase().split('.').pop();
    return extension === 'xlsx' || extension === 'xls';
  }

  /**
   * 获取文件大小
   */
  private async getFileSize(file: UploadFileInfo): Promise<number> {
    if (typeof file.data === 'string') {
      const stats = await fs.stat(file.data);
      return stats.size;
    }
    return 0;
  }
}
