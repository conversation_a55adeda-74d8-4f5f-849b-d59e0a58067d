import {
  Table,
  Column,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { RegionDict } from './region-dict.entity';
import { Photo } from './photo.entity';

export interface WaterSystemAttributes {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 编号 */
  code: string;
  /** 水系经度 */
  longitude?: number;
  /** 水系纬度 */
  latitude?: number;
  /** 水系长度/面积 */
  lengthArea?: string;
  /** 相关历史文献记载 */
  historicalRecords?: string;
  /** 所属区域ID */
  regionDictId: number;
}

/**
 * 水系表模型
 */
@Table({
  tableName: 'water_system',
  comment: '水系表',
})
export class WaterSystem
  extends Model<WaterSystemAttributes>
  implements WaterSystemAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '编号',
  })
  code: string;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: true,
    comment: '水系经度',
  })
  longitude: number;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: true,
    comment: '水系纬度',
  })
  latitude: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '水系长度/面积',
    field: 'length_area',
  })
  lengthArea: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '相关历史文献记载',
    field: 'historical_records',
  })
  historicalRecords: string;

  @ForeignKey(() => RegionDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '所属区域ID',
    field: 'region_dict_id',
  })
  regionDictId: number;

  // 关联关系
  @BelongsTo(() => RegionDict, 'regionDictId')
  regionDict: RegionDict;

  @HasMany(() => Photo, 'waterSystemId')
  photos: Photo[];
}
