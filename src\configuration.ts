import { Configuration, App } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import * as sequelize from '@midwayjs/sequelize';
import * as redis from '@midwayjs/redis';
import * as jwt from '@midwayjs/jwt';
import * as busboy from '@midwayjs/busboy';
// import * as staticFile from '@midwayjs/static-file';
import { join } from 'path';
import { DatabaseFilter } from './filter/database.filter';
import { NotFoundFilter } from './filter/notfound.filter';
import { NoAuthFilter } from './filter/noauth.filter';
import { CustomErrorFilter } from './filter/custom.filter';
import { DefaultErrorFilter } from './filter/default.filter';
import { UploadErrorFilter } from './filter/upload.filter';
import { RequestLoggerMiddleware } from './middleware/requestlogger.middleware';
import { FormatMiddleware } from './middleware/format.middleware';
import { JwtMiddleware } from './middleware/jwt.middleware';
import { AuthMiddleware } from './middleware/auth.middleware';

@Configuration({
  imports: [
    koa,
    validate,
    sequelize,
    redis,
    jwt,
    busboy,
    // staticFile, // 临时禁用静态文件中间件来测试路由问题
    {
      component: info,
      enabledEnvironment: ['local'],
    },
  ],
  importConfigs: [join(__dirname, './config')],
})
export class MainConfiguration {
  @App('koa')
  app: koa.Application;

  async onReady() {
    const env = this.app.getEnv();
    const middlewares = [JwtMiddleware, AuthMiddleware, FormatMiddleware];
    const filters = [
      NotFoundFilter,
      UploadErrorFilter, // 文件上传错误过滤器，优先处理
      NoAuthFilter,
      DatabaseFilter,
      CustomErrorFilter,
      DefaultErrorFilter,
    ];
    if (['local', 'unittest'].includes(env)) {
      middlewares.unshift(RequestLoggerMiddleware);
    }
    // add middleware
    this.app.useMiddleware(middlewares);
    // add filter
    this.app.useFilter(filters);
  }
}
