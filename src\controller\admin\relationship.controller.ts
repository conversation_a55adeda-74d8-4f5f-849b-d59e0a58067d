import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { RelationshipService } from '../../service/relationship.service';
import {
  CreateRelationshipDTO,
  UpdateRelationshipDTO,
  RelationshipQueryDTO,
  BatchCreateRelationshipDTO,
} from '../../dto/relationship.dto';
import { BatchUpdateStatusDTO } from '../../dto/dictionary.dto';

/**
 * 关系管理控制器
 */
@Controller('/admin/relationship', {
  middleware: [JwtMiddleware, AuthMiddleware],
})
export class AdminRelationshipController {
  @Inject()
  relationshipService: RelationshipService;

  /**
   * 创建关系
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateRelationshipDTO) {
    console.log('🔗 创建关系:', createDto);

    try {
      const data = await this.relationshipService.createRelationship(createDto);
      console.log('🔗 关系创建成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系创建失败:', error);
      throw error;
    }
  }

  /**
   * 更新关系
   */
  @Put('/:id')
  @Validate()
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateRelationshipDTO
  ) {
    console.log('🔗 更新关系:', { id, updateDto });

    try {
      const data = await this.relationshipService.updateRelationship(
        id,
        updateDto
      );
      console.log('🔗 关系更新成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系更新失败:', error);
      throw error;
    }
  }

  /**
   * 删除关系
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    console.log('🔗 删除关系:', id);

    try {
      await this.relationshipService.deleteRelationship(id);
      console.log('🔗 关系删除成功:', id);
      return { message: '删除成功' };
    } catch (error) {
      console.error('🔗 关系删除失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系详情
   */
  @Get('/:id')
  async getById(@Param('id') id: number) {
    console.log('🔗 获取关系详情:', id);

    try {
      const data = await this.relationshipService.getRelationshipById(id);
      console.log('🔗 关系详情获取成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系详情获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系列表（分页）
   */
  @Get('/')
  @Validate()
  async getList(@Query() queryDto: RelationshipQueryDTO) {
    console.log('🔗 关系列表查询开始:', queryDto);

    try {
      const data = await this.relationshipService.getRelationshipPage(queryDto);
      console.log('🔗 关系列表查询成功:', {
        total: data.total,
        page: data.page,
        pageSize: data.pageSize,
      });
      return data;
    } catch (error) {
      console.error('🔗 关系列表查询失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有关系（不分页）
   */
  @Get('/all')
  async getAll(@Query() filters?: any) {
    console.log('🔗 获取所有关系');

    try {
      const data = await this.relationshipService.getRelationshipList(filters);
      console.log('🔗 所有关系获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 所有关系获取失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建关系
   */
  @Post('/batch')
  @Validate()
  async batchCreate(@Body() batchDto: BatchCreateRelationshipDTO) {
    console.log('🔗 批量创建关系:', batchDto.relations.length);

    try {
      await this.relationshipService.batchCreateRelationships(
        batchDto.relations
      );
      console.log('🔗 批量创建关系成功');
      return { message: '批量创建成功' };
    } catch (error) {
      console.error('🔗 批量创建关系失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新状态
   */
  @Put('/batch-status')
  @Validate()
  async batchUpdateStatus(@Body() batchDto: BatchUpdateStatusDTO) {
    console.log('🔗 批量更新关系状态:', batchDto);

    try {
      await this.relationshipService.batchUpdateStatus(
        batchDto.ids,
        batchDto.status
      );
      console.log('🔗 批量更新关系状态成功');
      return { message: '批量更新成功' };
    } catch (error) {
      console.error('🔗 批量更新关系状态失败:', error);
      throw error;
    }
  }

  /**
   * 根据要素获取关联关系
   */
  @Get('/by-element/:elementType/:elementId')
  async getByElement(
    @Param('elementType') elementType: string,
    @Param('elementId') elementId: number
  ) {
    console.log('🔗 根据要素获取关联关系:', { elementType, elementId });

    try {
      const data = await this.relationshipService.getRelationsByElement(
        elementType,
        elementId
      );
      console.log('🔗 要素关联关系获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 要素关联关系获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系统计
   */
  @Get('/statistics/overview')
  async getStatistics(@Query() filters?: any) {
    console.log('🔗 获取关系统计');

    try {
      const data = await this.relationshipService.getRelationshipStatistics(
        filters
      );
      console.log('🔗 关系统计获取成功');
      return data;
    } catch (error) {
      console.error('🔗 关系统计获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取网络图数据
   */
  @Get('/network-graph')
  async getNetworkGraph(@Query() filters?: any) {
    console.log('🔗 获取网络图数据');

    try {
      const data = await this.relationshipService.getNetworkGraphData(filters);
      console.log('🔗 网络图数据获取成功:', {
        nodes: data.nodes.length,
        links: data.links.length,
        categories: data.categories.length,
      });
      return data;
    } catch (error) {
      console.error('🔗 网络图数据获取失败:', error);
      throw error;
    }
  }
}
