import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { MapService } from '../../service/map.service';
import { MapDataQueryDTO, DetailQueryDTO } from '../../dto/common.dto';

/**
 * 地图数据公开接口
 */
@Controller('/api/map')
export class PublicMapController {
  @Inject()
  mapService: MapService;

  /**
   * 获取地图数据
   */
  @Get('/data')
  @Validate()
  async getMapData(@Query() query: MapDataQueryDTO) {
    const data = await this.mapService.getMapData(query);
    return data;
  }

  /**
   * 获取实体详情数据
   */
  @Get('/detail')
  @Validate()
  async getDetail(@Query() query: DetailQueryDTO) {
    const data = await this.mapService.getDetailData(query.type, query.id);
    return data;
  }

  /**
   * 获取地图统计数据
   */
  @Get('/statistics')
  async getMapStatistics(@Query('regionId') regionId?: number) {
    const data = await this.mapService.getMapStatistics(regionId);
    return data;
  }
}
