import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { StatisticService } from '../../service/statistic.service';
import { HistoricalElementService } from '../../service/historical-element.service';
import { StatisticQueryDTO } from '../../dto/common.dto';

/**
 * 统计数据公开接口
 */
@Controller('/public/statistic')
export class PublicStatisticController {
  @Inject()
  statisticService: StatisticService;

  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 获取基础统计数据
   */
  @Get('/basic')
  @Validate()
  async getBasicStatistics(@Query() query: StatisticQueryDTO) {
    const data = await this.statisticService.getStatisticData(query);
    return data;
  }

  /**
   * 获取综合统计报告
   */
  @Get('/comprehensive')
  async getComprehensiveReport(@Query('regionId') regionId?: number) {
    // 验证regionId参数
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;
    const data = await this.statisticService.getComprehensiveReport(
      validRegionId
    );
    return data;
  }

  /**
   * 获取时间轴数据
   */
  @Get('/timeline')
  async getTimelineData(@Query('regionId') regionId?: number) {
    // 验证regionId参数
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;
    const data = await this.historicalElementService.getTimelineData(
      validRegionId
    );
    return data;
  }

  /**
   * 获取区域分布统计
   */
  @Get('/region-distribution')
  async getRegionDistribution() {
    const data = await this.statisticService.getStatisticData({});
    return data.regionStats;
  }

  /**
   * 获取数据概览
   */
  @Get('/overview')
  async getDataOverview(@Query('regionId') regionId?: number) {
    // 验证regionId参数
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;
    const data = await this.statisticService.getStatisticData({
      regionId: validRegionId,
    });
    return {
      totalCounts: data.counts,
      regionStats: data.regionStats,
      timelineData: data.timelineData,
    };
  }
}
