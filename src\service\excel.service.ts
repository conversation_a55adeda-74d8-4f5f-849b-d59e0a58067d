import { Provide, Inject } from '@midwayjs/core';
import {
  CreateHistoricalElementDTO,
  CreateMountainDTO,
  CreateWaterSystemDTO,
} from '../dto/entity.dto';
import { ExcelValidationError, ExcelParseResult } from '../common/xlsxUtils';
import { HistoricalElementService } from './historical-element.service';
import { MountainService } from './mountain.service';
import { WaterSystemService } from './water-system.service';
import { BaseExcelService } from './base-excel.service';
import { ExcelConfigs } from '../config/excel-configs';

@Provide()
export class ExcelService extends BaseExcelService {
  @Inject()
  historicalElementService: HistoricalElementService;

  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  /**
   * 生成历史要素导入模板
   */
  async generateHistoricalElementTemplate(): Promise<Buffer> {
    return this.generateTemplate(ExcelConfigs.HistoricalElement.Import);
  }

  /**
   * 解析历史要素Excel文件
   */
  async parseHistoricalElementExcel(
    filePath: string
  ): Promise<ExcelParseResult<CreateHistoricalElementDTO>> {
    const result = await this.parseExcel<any>(
      filePath,
      ExcelConfigs.HistoricalElement.Import,
      this.createHistoricalElementValidator()
    );

    // 转换数据类型（异步转换）
    if (result.success && result.data) {
      result.data = await this.convertBatchDataAsync(result.data, item =>
        this.convertToHistoricalElementDTO(item)
      );
    }

    return result as ExcelParseResult<CreateHistoricalElementDTO>;
  }

  /**
   * 创建历史要素专用的验证器
   */
  private createHistoricalElementValidator() {
    return this.createValidatorWithDuplicateCheck(
      ['编号'], // 需要检查唯一性的字段
      this.validateHistoricalElementData.bind(this) // 自定义验证函数
    );
  }

  /**
   * 异步批量转换数据
   */
  private async convertBatchDataAsync<T, R>(
    data: T[],
    converter: (item: T) => Promise<R>
  ): Promise<R[]> {
    const results: R[] = [];
    for (const item of data) {
      const converted = await converter(item);
      results.push(converted);
    }
    return results;
  }

  /**
   * 转换Excel数据为历史要素DTO（异步）
   */
  private async convertToHistoricalElementDTO(
    data: any
  ): Promise<CreateHistoricalElementDTO> {
    const dto: CreateHistoricalElementDTO = {
      name: this.parseString(data.name),
      code: this.parseString(data.code),
      regionDictId: 0, // 临时值，下面会设置
    };

    // 根据区域名称获取区域ID
    if (data.regionName && data.regionName.toString().trim() !== '') {
      const regionName = this.parseString(data.regionName);
      if (regionName) {
        const regionId = await this.getRegionIdByName(regionName);
        if (regionId) {
          dto.regionDictId = regionId;
        } else {
          throw new Error(`区域名称 "${regionName}" 不存在或已禁用`);
        }
      }
    } else {
      throw new Error('区域名称不能为空');
    }

    // 根据类型名称获取类型ID（可选字段）
    if (data.typeName && data.typeName.toString().trim() !== '') {
      const typeName = this.parseString(data.typeName);
      if (typeName) {
        const typeId = await this.getTypeIdByName(typeName);
        if (typeId) {
          dto.typeDictId = typeId;
        } else {
          throw new Error(`类型名称 "${typeName}" 不存在或已禁用`);
        }
      }
    }

    // 其他可选字段
    if (
      data.constructionLongitude &&
      data.constructionLongitude.toString().trim() !== ''
    ) {
      dto.constructionLongitude = this.parseNumber(data.constructionLongitude)!;
    }

    if (
      data.constructionLatitude &&
      data.constructionLatitude.toString().trim() !== ''
    ) {
      dto.constructionLatitude = this.parseNumber(data.constructionLatitude)!;
    }

    if (
      data.locationDescription &&
      data.locationDescription.toString().trim() !== ''
    ) {
      dto.locationDescription = this.parseString(data.locationDescription);
    }

    if (
      data.constructionTime &&
      data.constructionTime.toString().trim() !== ''
    ) {
      dto.constructionTime = this.parseDate(data.constructionTime)!;
    }

    if (
      data.historicalRecords &&
      data.historicalRecords.toString().trim() !== ''
    ) {
      dto.historicalRecords = this.parseString(data.historicalRecords);
    }

    return dto;
  }

  /**
   * 验证历史要素数据
   */
  private async validateHistoricalElementData(
    data: any,
    rowIndex: number
  ): Promise<ExcelValidationError[]> {
    const errors: ExcelValidationError[] = [];

    // 基础格式验证
    errors.push(
      ...this.validateBasicFields(
        data,
        ExcelConfigs.HistoricalElement.Import,
        rowIndex
      )
    );

    // 历史要素特定的验证
    errors.push(...this.validateHistoricalElementSpecific(data, rowIndex));

    // 如果基础验证通过，进行业务规则验证
    if (errors.length === 0) {
      const businessErrors = await this.validateBusinessRules(data, rowIndex);
      errors.push(...businessErrors);
    }

    return errors;
  }

  /**
   * 历史要素特定的字段验证
   */
  private validateHistoricalElementSpecific(
    data: any,
    rowIndex: number
  ): ExcelValidationError[] {
    const errors: ExcelValidationError[] = [];

    // 验证名称长度
    if (data.name && data.name.toString().length > 255) {
      errors.push({
        row: rowIndex,
        field: '名称',
        value: data.name,
        message: '名称长度不能超过255个字符',
      });
    }

    // 验证编号长度
    if (data.code && data.code.toString().length > 50) {
      errors.push({
        row: rowIndex,
        field: '编号',
        value: data.code,
        message: '编号长度不能超过50个字符',
      });
    }

    // 验证经纬度范围
    const longitude = this.parseNumber(data.constructionLongitude);
    if (longitude !== null && (longitude < -180 || longitude > 180)) {
      errors.push({
        row: rowIndex,
        field: '建筑经度',
        value: longitude,
        message: '建筑经度范围应在-180到180之间',
      });
    }

    const latitude = this.parseNumber(data.constructionLatitude);
    if (latitude !== null && (latitude < -90 || latitude > 90)) {
      errors.push({
        row: rowIndex,
        field: '建筑纬度',
        value: latitude,
        message: '建筑纬度范围应在-90到90之间',
      });
    }

    // 验证建造时间（可选）
    if (
      data.constructionTime &&
      data.constructionTime.toString().trim() !== ''
    ) {
      const constructionDate = this.parseDate(data.constructionTime);
      if (!constructionDate) {
        errors.push({
          row: rowIndex,
          field: '建造时间',
          value: data.constructionTime,
          message: '建造时间格式不正确，请使用YYYY-MM-DD格式',
        });
      } else if (constructionDate > new Date()) {
        errors.push({
          row: rowIndex,
          field: '建造时间',
          value: data.constructionTime,
          message: '建造时间不能晚于当前时间',
        });
      }
    }

    return errors;
  }

  /**
   * 验证业务规则
   */
  private async validateBusinessRules(
    data: any,
    rowIndex: number
  ): Promise<ExcelValidationError[]> {
    const errors: ExcelValidationError[] = [];

    try {
      // 检查编号是否已存在于数据库中
      const existingElement = await this.checkCodeExists(
        data.code.toString().trim()
      );
      if (existingElement) {
        errors.push({
          row: rowIndex,
          field: '编号',
          value: data.code,
          message: '编号已存在于数据库中',
        });
      }

      // 验证区域名称是否存在
      if (data.regionName && data.regionName.toString().trim() !== '') {
        const regionName = this.parseString(data.regionName);
        if (regionName) {
          const regionId = await this.getRegionIdByName(regionName);
          if (!regionId) {
            errors.push({
              row: rowIndex,
              field: '区域名称',
              value: regionName,
              message: '指定的区域名称不存在或已禁用',
            });
          }
        }
      } else {
        errors.push({
          row: rowIndex,
          field: '区域名称',
          value: data.regionName,
          message: '区域名称不能为空',
        });
      }

      // 验证类型名称是否存在（如果提供了的话）
      if (data.typeName && data.typeName.toString().trim() !== '') {
        const typeName = this.parseString(data.typeName);
        if (typeName) {
          const typeId = await this.getTypeIdByName(typeName);
          if (!typeId) {
            errors.push({
              row: rowIndex,
              field: '类型名称',
              value: typeName,
              message: '指定的类型名称不存在或已禁用',
            });
          }
        }
      }
    } catch (error) {
      errors.push({
        row: rowIndex,
        field: 'system',
        value: '',
        message: `数据验证失败: ${error.message}`,
      });
    }

    return errors;
  }

  /**
   * 检查编号是否已存在
   */
  private async checkCodeExists(code: string): Promise<boolean> {
    try {
      const existing = await this.historicalElementService.findByCode(code);
      return !!existing;
    } catch (error) {
      console.warn('检查编号存在性失败:', error);
      return false;
    }
  }

  /**
   * 根据区域名称查找区域ID
   */
  private async getRegionIdByName(regionName: string): Promise<number | null> {
    try {
      const { RegionDict } = await import('../entity');
      const region = await RegionDict.findOne({
        where: {
          regionName: regionName.trim(),
          status: 1, // 只查找启用的区域
        },
      });
      return region ? region.id : null;
    } catch (error) {
      console.warn('根据区域名称查找ID失败:', error);
      return null;
    }
  }

  /**
   * 根据类型名称查找类型ID
   */
  private async getTypeIdByName(typeName: string): Promise<number | null> {
    try {
      const { TypeDict } = await import('../entity');
      const type = await TypeDict.findOne({
        where: {
          typeName: typeName.trim(),
          status: 1, // 只查找启用的类型
        },
      });
      return type ? type.id : null;
    } catch (error) {
      console.warn('根据类型名称查找ID失败:', error);
      return null;
    }
  }

  /**
   * 生成并保存模板文件到public目录
   */
  async generateTemplateFile(): Promise<string> {
    return super.ensureTemplateExists(
      ExcelConfigs.HistoricalElement.Import,
      'historical_element_import_template.xlsx'
    );
  }

  /**
   * 检查模板文件是否存在，不存在则生成
   */
  async ensureTemplateExists(): Promise<string> {
    return super.ensureTemplateExists(
      ExcelConfigs.HistoricalElement.Import,
      'historical_element_import_template.xlsx'
    );
  }

  // ==================== 山塬相关方法 ====================

  /**
   * 生成山塬导入模板
   */
  async generateMountainTemplate(): Promise<Buffer> {
    return this.generateTemplate(ExcelConfigs.Mountain.Import);
  }

  /**
   * 解析山塬Excel文件
   */
  async parseMountainExcel(
    filePath: string
  ): Promise<ExcelParseResult<CreateMountainDTO>> {
    const result = await this.parseExcel<any>(
      filePath,
      ExcelConfigs.Mountain.Import,
      this.createMountainValidator()
    );

    // 转换数据类型（异步转换）
    if (result.success && result.data) {
      result.data = await this.convertBatchDataAsync(result.data, item =>
        this.convertToMountainDTOAsync(item)
      );
    }

    return result as ExcelParseResult<CreateMountainDTO>;
  }

  /**
   * 导出山塬数据到Excel
   */
  async exportMountainToExcel(mountains: any[]): Promise<Buffer> {
    const config = ExcelConfigs.Mountain.Export;
    const workbook = await this.generateTemplate({
      ...config,
      exampleData: mountains,
    });
    return workbook;
  }

  /**
   * 获取山塬导入模板
   */
  async getMountainImportTemplate(): Promise<string> {
    return super.ensureTemplateExists(
      ExcelConfigs.Mountain.Import,
      'mountain_import_template.xlsx'
    );
  }

  /**
   * 创建山塬专用的验证器
   */
  private createMountainValidator() {
    return this.createValidatorWithDuplicateCheck(
      ['编号'], // 需要检查唯一性的字段
      this.validateMountainData.bind(this) // 自定义验证函数
    );
  }

  /**
   * 验证山塬数据
   */
  private async validateMountainData(
    data: any,
    rowIndex: number
  ): Promise<ExcelValidationError[]> {
    const errors: ExcelValidationError[] = [];

    // 验证基础字段
    errors.push(
      ...this.validateBasicFields(data, ExcelConfigs.Mountain.Import, rowIndex)
    );

    // 验证高度
    if (data.height !== undefined && data.height !== null) {
      const height = this.parseNumber(data.height);
      if (height === null || height < 0) {
        errors.push({
          row: rowIndex,
          field: '高度',
          value: data.height,
          message: '高度必须是非负数',
        });
      }
    }

    // 验证经纬度
    if (data.longitude !== undefined && data.longitude !== null) {
      const longitude = this.parseNumber(data.longitude);
      if (longitude !== null && (longitude < -180 || longitude > 180)) {
        errors.push({
          row: rowIndex,
          field: '经度',
          value: data.longitude,
          message: '经度范围应在-180到180之间',
        });
      }
    }

    if (data.latitude !== undefined && data.latitude !== null) {
      const latitude = this.parseNumber(data.latitude);
      if (latitude !== null && (latitude < -90 || latitude > 90)) {
        errors.push({
          row: rowIndex,
          field: '纬度',
          value: data.latitude,
          message: '纬度范围应在-90到90之间',
        });
      }
    }

    // 验证区域名称
    if (data.regionName && data.regionName.toString().trim() !== '') {
      const regionName = this.parseString(data.regionName);
      if (regionName) {
        const regionId = await this.getRegionIdByName(regionName);
        if (!regionId) {
          errors.push({
            row: rowIndex,
            field: '区域名称',
            value: regionName,
            message: '指定的区域名称不存在或已禁用',
          });
        }
      }
    } else {
      errors.push({
        row: rowIndex,
        field: '区域名称',
        value: data.regionName,
        message: '区域名称不能为空',
      });
    }

    return errors;
  }

  /**
   * 转换为山塬DTO（异步）
   */
  private async convertToMountainDTOAsync(
    item: any
  ): Promise<CreateMountainDTO> {
    const dto: CreateMountainDTO = {
      name: this.parseString(item.name),
      code: this.parseString(item.code),
      longitude: this.parseNumber(item.longitude),
      latitude: this.parseNumber(item.latitude),
      height: this.parseNumber(item.height),
      historicalRecords: this.parseString(item.historicalRecords),
      regionDictId: 0, // 临时值，下面会设置
    };

    // 根据区域名称获取区域ID
    if (item.regionName && item.regionName.toString().trim() !== '') {
      const regionName = this.parseString(item.regionName);
      if (regionName) {
        const regionId = await this.getRegionIdByName(regionName);
        if (regionId) {
          dto.regionDictId = regionId;
        } else {
          throw new Error(`区域名称 "${regionName}" 不存在或已禁用`);
        }
      }
    } else {
      throw new Error('区域名称不能为空');
    }

    return dto;
  }

  // ==================== 水系相关方法 ====================

  /**
   * 生成水系导入模板
   */
  async generateWaterSystemTemplate(): Promise<Buffer> {
    return this.generateTemplate(ExcelConfigs.WaterSystem.Import);
  }

  /**
   * 解析水系Excel文件
   */
  async parseWaterSystemExcel(
    filePath: string
  ): Promise<ExcelParseResult<CreateWaterSystemDTO>> {
    const result = await this.parseExcel<any>(
      filePath,
      ExcelConfigs.WaterSystem.Import,
      this.createWaterSystemValidator()
    );

    // 转换数据类型（异步转换）
    if (result.success && result.data) {
      result.data = await this.convertBatchDataAsync(result.data, item =>
        this.convertToWaterSystemDTOAsync(item)
      );
    }

    return result as ExcelParseResult<CreateWaterSystemDTO>;
  }

  /**
   * 导出水系数据到Excel
   */
  async exportWaterSystemToExcel(waterSystems: any[]): Promise<Buffer> {
    const config = ExcelConfigs.WaterSystem.Export;
    const workbook = await this.generateTemplate({
      ...config,
      exampleData: waterSystems,
    });
    return workbook;
  }

  /**
   * 获取水系导入模板
   */
  async getWaterSystemImportTemplate(): Promise<string> {
    return super.ensureTemplateExists(
      ExcelConfigs.WaterSystem.Import,
      'water_system_import_template.xlsx'
    );
  }

  /**
   * 创建水系专用的验证器
   */
  private createWaterSystemValidator() {
    return this.createValidatorWithDuplicateCheck(
      ['编号'], // 需要检查唯一性的字段
      this.validateWaterSystemData.bind(this) // 自定义验证函数
    );
  }

  /**
   * 验证水系数据
   */
  private async validateWaterSystemData(
    data: any,
    rowIndex: number
  ): Promise<ExcelValidationError[]> {
    const errors: ExcelValidationError[] = [];

    // 验证基础字段
    errors.push(
      ...this.validateBasicFields(
        data,
        ExcelConfigs.WaterSystem.Import,
        rowIndex
      )
    );

    // 验证长度/面积（可选字段，如果提供了则验证格式）
    if (data.lengthArea && this.parseString(data.lengthArea).trim() === '') {
      errors.push({
        row: rowIndex,
        field: '长度/面积',
        value: data.lengthArea,
        message: '长度/面积不能为空字符串',
      });
    }

    // 验证经纬度
    if (data.longitude !== undefined && data.longitude !== null) {
      const longitude = this.parseNumber(data.longitude);
      if (longitude !== null && (longitude < -180 || longitude > 180)) {
        errors.push({
          row: rowIndex,
          field: '经度',
          value: data.longitude,
          message: '经度范围应在-180到180之间',
        });
      }
    }

    if (data.latitude !== undefined && data.latitude !== null) {
      const latitude = this.parseNumber(data.latitude);
      if (latitude !== null && (latitude < -90 || latitude > 90)) {
        errors.push({
          row: rowIndex,
          field: '纬度',
          value: data.latitude,
          message: '纬度范围应在-90到90之间',
        });
      }
    }

    // 验证区域名称
    if (data.regionName && data.regionName.toString().trim() !== '') {
      const regionName = this.parseString(data.regionName);
      if (regionName) {
        const regionId = await this.getRegionIdByName(regionName);
        if (!regionId) {
          errors.push({
            row: rowIndex,
            field: '区域名称',
            value: regionName,
            message: '指定的区域名称不存在或已禁用',
          });
        }
      }
    } else {
      errors.push({
        row: rowIndex,
        field: '区域名称',
        value: data.regionName,
        message: '区域名称不能为空',
      });
    }

    return errors;
  }

  /**
   * 转换为水系DTO（异步）
   */
  private async convertToWaterSystemDTOAsync(
    item: any
  ): Promise<CreateWaterSystemDTO> {
    const lengthArea = this.parseString(item.lengthArea);
    const dto: CreateWaterSystemDTO = {
      name: this.parseString(item.name),
      code: this.parseString(item.code),
      longitude: this.parseNumber(item.longitude),
      latitude: this.parseNumber(item.latitude),
      lengthArea: lengthArea && lengthArea.trim() ? lengthArea : undefined,
      historicalRecords: this.parseString(item.historicalRecords),
      regionDictId: 0, // 临时值，下面会设置
    };

    // 根据区域名称获取区域ID
    if (item.regionName && item.regionName.toString().trim() !== '') {
      const regionName = this.parseString(item.regionName);
      if (regionName) {
        const regionId = await this.getRegionIdByName(regionName);
        if (regionId) {
          dto.regionDictId = regionId;
        } else {
          throw new Error(`区域名称 "${regionName}" 不存在或已禁用`);
        }
      }
    } else {
      throw new Error('区域名称不能为空');
    }

    return dto;
  }
}
