import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 山塬创建DTO
 */
export class CreateMountainDTO {
  @Rule(RuleType.string().required().max(255))
  name: string;

  @Rule(RuleType.string().required().max(50))
  code: string;

  @Rule(RuleType.number().optional().allow(null).min(-180).max(180))
  longitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(-90).max(90))
  latitude?: number;

  @Rule(RuleType.number().integer().optional().allow(null).min(0))
  height?: number;

  @Rule(RuleType.string().optional().allow(null))
  historicalRecords?: string;

  @Rule(RuleType.number().integer().required())
  regionDictId: number;
}

/**
 * 山塬更新DTO
 */
export class UpdateMountainDTO {
  @Rule(RuleType.string().optional().allow(null).max(255))
  name?: string;

  @Rule(RuleType.string().optional().allow(null).max(50))
  code?: string;

  @Rule(RuleType.number().optional().allow(null).min(-180).max(180))
  longitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(-90).max(90))
  latitude?: number;

  @Rule(RuleType.number().integer().optional().allow(null).min(0))
  height?: number;

  @Rule(RuleType.string().optional().allow(null))
  historicalRecords?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  regionDictId?: number;
}

/**
 * 水系创建DTO
 */
export class CreateWaterSystemDTO {
  @Rule(RuleType.string().required().max(255))
  name: string;

  @Rule(RuleType.string().required().max(50))
  code: string;

  @Rule(RuleType.number().optional().allow(null).min(-180).max(180))
  longitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(-90).max(90))
  latitude?: number;

  @Rule(RuleType.string().optional().allow(null).max(50))
  lengthArea?: string;

  @Rule(RuleType.string().optional().allow(null))
  historicalRecords?: string;

  @Rule(RuleType.number().integer().required())
  regionDictId: number;
}

/**
 * 水系更新DTO
 */
export class UpdateWaterSystemDTO {
  @Rule(RuleType.string().optional().allow(null).max(255))
  name?: string;

  @Rule(RuleType.string().optional().allow(null).max(50))
  code?: string;

  @Rule(RuleType.number().optional().allow(null).min(-180).max(180))
  longitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(-90).max(90))
  latitude?: number;

  @Rule(RuleType.string().optional().allow(null).max(50))
  lengthArea?: string;

  @Rule(RuleType.string().optional().allow(null))
  historicalRecords?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  regionDictId?: number;
}

/**
 * 历史要素创建DTO
 */
export class CreateHistoricalElementDTO {
  @Rule(RuleType.string().required().max(255))
  name: string;

  @Rule(RuleType.string().required().max(50))
  code: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  typeDictId?: number;

  @Rule(RuleType.number().optional().allow(null).min(-180).max(180))
  constructionLongitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(-90).max(90))
  constructionLatitude?: number;

  @Rule(RuleType.string().optional().allow(null))
  locationDescription?: string;

  @Rule(RuleType.date().optional().allow(null))
  constructionTime?: Date;

  @Rule(RuleType.string().optional().allow(null))
  historicalRecords?: string;

  @Rule(RuleType.number().integer().required())
  regionDictId: number;
}

/**
 * 历史要素更新DTO
 */
export class UpdateHistoricalElementDTO {
  @Rule(RuleType.string().optional().allow(null).max(255))
  name?: string;

  @Rule(RuleType.string().optional().allow(null).max(50))
  code?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  typeDictId?: number;

  @Rule(RuleType.number().optional().allow(null).min(-180).max(180))
  constructionLongitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(-90).max(90))
  constructionLatitude?: number;

  @Rule(RuleType.string().optional().allow(null))
  locationDescription?: string;

  @Rule(RuleType.date().optional().allow(null))
  constructionTime?: Date;

  @Rule(RuleType.string().optional().allow(null))
  historicalRecords?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  regionDictId?: number;
}

/**
 * 关系创建DTO
 */
export class CreateRelationshipDTO {
  @Rule(RuleType.string().required().max(255))
  name: string;

  @Rule(RuleType.string().required().max(50))
  code: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  relationDictId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentRelationshipId?: number;

  @Rule(RuleType.number().integer().required())
  sourceId: number;

  @Rule(RuleType.number().integer().required())
  targetId: number;
}

/**
 * 关系更新DTO
 */
export class UpdateRelationshipDTO {
  @Rule(RuleType.string().optional().allow(null).max(255))
  name?: string;

  @Rule(RuleType.string().optional().allow(null).max(50))
  code?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  relationDictId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentRelationshipId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sourceId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  targetId?: number;
}

/**
 * 照片创建DTO
 */
export class CreatePhotoDTO {
  @Rule(RuleType.string().required().max(255))
  name: string;

  @Rule(RuleType.string().required().max(255))
  url: string;

  @Rule(RuleType.string().optional().max(255).allow('').allow(null))
  path?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  mountainId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  waterSystemId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  historicalElementId?: number;
}

/**
 * 照片更新DTO
 */
export class UpdatePhotoDTO {
  @Rule(RuleType.string().optional().allow(null).max(255))
  name?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  url?: string;

  @Rule(RuleType.string().optional().max(255).allow('').allow(null))
  path?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  mountainId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  waterSystemId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  historicalElementId?: number;
}

// ==================== 区域字典相关 DTO ====================

/**
 * 创建区域字典DTO
 */
export class CreateRegionDictDTO {
  @Rule(RuleType.string().required().max(50))
  regionCode: string;

  @Rule(RuleType.string().required().max(255))
  regionName: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().min(0).max(1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  regionDesc?: string;
}

/**
 * 更新区域字典DTO
 */
export class UpdateRegionDictDTO {
  @Rule(RuleType.string().optional().allow(null).max(50))
  regionCode?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  regionName?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().min(0).max(1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  regionDesc?: string;
}

// ==================== 类型字典相关 DTO ====================

/**
 * 创建类型字典DTO
 */
export class CreateTypeDictDTO {
  @Rule(RuleType.string().required().max(50))
  typeCode: string;

  @Rule(RuleType.string().required().max(255))
  typeName: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().min(0).max(1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  typeDesc?: string;
}

/**
 * 更新类型字典DTO
 */
export class UpdateTypeDictDTO {
  @Rule(RuleType.string().optional().allow(null).max(50))
  typeCode?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  typeName?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().min(0).max(1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  typeDesc?: string;
}

// ==================== 关系字典相关 DTO ====================

/**
 * 创建关系字典DTO
 */
export class CreateRelationshipDictDTO {
  @Rule(RuleType.string().required().max(50))
  relationCode: string;

  @Rule(RuleType.string().required().max(255))
  relationName: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().min(0).max(1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  relationDesc?: string;
}

/**
 * 更新关系字典DTO
 */
export class UpdateRelationshipDictDTO {
  @Rule(RuleType.string().optional().allow(null).max(50))
  relationCode?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  relationName?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().min(0).max(1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  relationDesc?: string;
}
