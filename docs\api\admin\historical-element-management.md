# 历史要素管理 API 文档

## 概述

历史要素管理模块提供历史文化要素的完整管理功能，包括古建筑、遗址、文物等历史文化遗产的创建、更新、删除、查询等操作，支持按类型、区域、时间范围筛选和时间轴数据展示。

## 相关文档

- [历史要素导入 API 文档](./historical-element-import.md) - Excel批量导入功能详细说明

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

### 常见错误码
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 业务错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 参数校验错误 |
| 500 | 服务器内部错误 |

---

## 创建历史要素

### 接口信息

- **URL**: `/admin/historical-element`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 历史要素名称，最大255字符 |
| code | string | 是 | 历史要素编号，最大50字符 |
| typeDictId | number | 否 | 所属类型ID |
| constructionLongitude | number | 是 | 建筑经度，范围-180到180 |
| constructionLatitude | number | 是 | 建筑纬度，范围-90到90 |
| locationDescription | string | 否 | 位置描述 |
| constructionTime | string | 否 | 建造时间，ISO日期格式 |
| historicalRecords | string | 否 | 历史文献记载 |
| regionDictId | number | 是 | 所属区域ID |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/historical-element" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "大雁塔",
    "code": "DYT001",
    "typeDictId": 1,
    "constructionLongitude": 108.9640,
    "constructionLatitude": 34.2180,
    "locationDescription": "位于西安市雁塔区大慈恩寺内",
    "constructionTime": "652-01-01T00:00:00.000Z",
    "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔。",
    "regionDictId": 1
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "大雁塔",
    "code": "DYT001",
    "typeDictId": 1,
    "constructionLongitude": 108.9640,
    "constructionLatitude": 34.2180,
    "locationDescription": "位于西安市雁塔区大慈恩寺内",
    "constructionTime": "652-01-01T00:00:00.000Z",
    "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔。",
    "regionDictId": 1,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

---

## 更新历史要素

### 接口信息

- **URL**: `/admin/historical-element/{id}`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 历史要素ID |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 历史要素名称 |
| code | string | 否 | 历史要素编号 |
| typeDictId | number | 否 | 所属类型ID |
| constructionLongitude | number | 否 | 建筑经度 |
| constructionLatitude | number | 否 | 建筑纬度 |
| locationDescription | string | 否 | 位置描述 |
| constructionTime | string | 否 | 建造时间 |
| historicalRecords | string | 否 | 历史文献记载 |
| regionDictId | number | 否 | 所属区域ID |

### 请求示例

```bash
curl -X PUT "http://localhost:7001/admin/historical-element/1" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "locationDescription": "位于西安市雁塔区大慈恩寺内，是西安的标志性建筑",
    "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔，是现存最早、规模最大的唐代四方楼阁式砖塔。"
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "大雁塔",
    "code": "DYT001",
    "typeDictId": 1,
    "constructionLongitude": 108.9640,
    "constructionLatitude": 34.2180,
    "locationDescription": "位于西安市雁塔区大慈恩寺内，是西安的标志性建筑",
    "constructionTime": "652-01-01T00:00:00.000Z",
    "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔，是现存最早、规模最大的唐代四方楼阁式砖塔。",
    "regionDictId": 1,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

---

## 删除历史要素

### 接口信息

- **URL**: `/admin/historical-element/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 历史要素ID |

### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deletePhotos | string | 否 | 是否同时删除关联照片，可选值：true、false，默认false |

### 请求示例

```bash
# 删除历史要素但保留关联照片（默认行为，外键设置为NULL）
curl -X DELETE "http://localhost:7001/admin/historical-element/1" \
  -H "Authorization: Bearer {token}"

# 删除历史要素并同时删除关联照片
curl -X DELETE "http://localhost:7001/admin/historical-element/1?deletePhotos=true" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "删除成功",
    "deletedPhotos": false
  }
}
```

---

## 获取历史要素列表

### 接口信息

- **URL**: `/admin/historical-element`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |
| typeId | number | 否 | 类型ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/historical-element?page=1&pageSize=10&keyword=大雁塔&typeId=1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001",
        "typeDictId": 1,
        "constructionLongitude": 108.9640,
        "constructionLatitude": 34.2180,
        "locationDescription": "位于西安市雁塔区大慈恩寺内",
        "constructionTime": "652-01-01T00:00:00.000Z",
        "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔。",
        "regionDictId": 1,
        "typeDict": {
          "id": 1,
          "typeName": "佛塔",
          "typeCode": "PAGODA"
        },
        "regionDict": {
          "id": 1,
          "regionName": "关中地区",
          "regionCode": "GUANZHONG"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

---

## 获取历史要素详情

### 接口信息

- **URL**: `/admin/historical-element/{id}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 历史要素ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/historical-element/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "大雁塔",
    "code": "DYT001",
    "typeDictId": 1,
    "constructionLongitude": 108.9640,
    "constructionLatitude": 34.2180,
    "locationDescription": "位于西安市雁塔区大慈恩寺内",
    "constructionTime": "652-01-01T00:00:00.000Z",
    "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔。",
    "regionDictId": 1,
    "typeDict": {
      "id": 1,
      "typeName": "佛塔",
      "typeCode": "PAGODA"
    },
    "regionDict": {
      "id": 1,
      "regionName": "关中地区",
      "regionCode": "GUANZHONG"
    },
    "photos": [
      {
        "id": 1,
        "name": "大雁塔全景",
        "url": "/public/uploads/2024/01/15/dayanta_1642234567890.jpg"
      }
    ]
  }
}
```

---

## 批量导入历史要素

### 接口信息

- **URL**: `/admin/historical-element/batch-import`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| elements | array | 是 | 历史要素数据数组，每个元素包含创建历史要素所需的所有字段 |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/historical-element/batch-import" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "elements": [
      {
        "name": "大雁塔",
        "code": "DYT001",
        "typeDictId": 1,
        "constructionLongitude": 108.9640,
        "constructionLatitude": 34.2180,
        "constructionTime": "652-01-01T00:00:00.000Z",
        "regionDictId": 1
      },
      {
        "name": "小雁塔",
        "code": "XYT001",
        "typeDictId": 1,
        "constructionLongitude": 108.9395,
        "constructionLatitude": 34.2370,
        "constructionTime": "707-01-01T00:00:00.000Z",
        "regionDictId": 1
      }
    ]
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "批量导入成功"
  }
}
```

---

## 获取历史要素统计

### 接口信息

- **URL**: `/admin/historical-element/statistics/overview`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选特定区域的统计数据 |
| typeId | number | 否 | 类型ID，筛选特定类型的统计数据 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/historical-element/statistics/overview?regionId=1&typeId=1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 50,
    "byType": [
      {
        "typeId": 1,
        "typeName": "佛塔",
        "count": 15
      },
      {
        "typeId": 2,
        "typeName": "古建筑",
        "count": 20
      }
    ],
    "byRegion": [
      {
        "regionId": 1,
        "regionName": "关中地区",
        "count": 35
      },
      {
        "regionId": 2,
        "regionName": "陕北地区",
        "count": 15
      }
    ],
    "byPeriod": [
      {
        "period": "唐代",
        "count": 25
      },
      {
        "period": "宋代",
        "count": 15
      }
    ]
  }
}
```

---

## 根据类型获取历史要素

### 接口信息

- **URL**: `/admin/historical-element/by-type/{typeId}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| typeId | number | 是 | 类型ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/historical-element/by-type/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "大雁塔",
      "code": "DYT001",
      "constructionLongitude": 108.9640,
      "constructionLatitude": 34.2180,
      "constructionTime": "652-01-01T00:00:00.000Z",
      "regionDictId": 1
    },
    {
      "id": 2,
      "name": "小雁塔",
      "code": "XYT001",
      "constructionLongitude": 108.9395,
      "constructionLatitude": 34.2370,
      "constructionTime": "707-01-01T00:00:00.000Z",
      "regionDictId": 1
    }
  ]
}
```

---

## 根据区域获取历史要素

### 接口信息

- **URL**: `/admin/historical-element/by-region/{regionId}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 是 | 区域ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/historical-element/by-region/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "大雁塔",
      "code": "DYT001",
      "typeDictId": 1,
      "constructionLongitude": 108.9640,
      "constructionLatitude": 34.2180,
      "constructionTime": "652-01-01T00:00:00.000Z"
    },
    {
      "id": 3,
      "name": "华清池",
      "code": "HQC001",
      "typeDictId": 2,
      "constructionLongitude": 109.2120,
      "constructionLatitude": 34.3620,
      "constructionTime": "747-01-01T00:00:00.000Z"
    }
  ]
}
```

---

## 根据建造时间范围查询

### 接口信息

- **URL**: `/admin/historical-element/by-construction-time`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startTime | string | 否 | 开始时间，ISO日期格式 |
| endTime | string | 否 | 结束时间，ISO日期格式 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/historical-element/by-construction-time?startTime=600-01-01T00:00:00.000Z&endTime=800-01-01T00:00:00.000Z" \
  -H "Authorization: Bearer {token}"
```

---

## 获取时间轴数据

### 接口信息

- **URL**: `/admin/historical-element/timeline`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选特定区域的时间轴数据 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/historical-element/timeline?regionId=1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "year": 652,
      "count": 1,
      "elements": [
        {
          "id": 1,
          "name": "大雁塔",
          "type": "historical_element"
        }
      ]
    },
    {
      "year": 707,
      "count": 1,
      "elements": [
        {
          "id": 2,
          "name": "小雁塔",
          "type": "historical_element"
        }
      ]
    }
  ]
}
```

---

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 历史要素唯一标识 |
| name | string | 历史要素名称 |
| code | string | 历史要素编号 |
| typeDictId | number | 所属类型ID |
| constructionLongitude | number | 建筑经度坐标（WGS84） |
| constructionLatitude | number | 建筑纬度坐标（WGS84） |
| locationDescription | string | 位置描述 |
| constructionTime | string | 建造时间 |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| typeDict | object | 类型详细信息 |
| regionDict | object | 区域详细信息 |
| photos | array | 关联的照片列表 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

---

## 错误处理

### 常见错误

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 历史要素名称不能为空 | 创建或更新时名称为空 |
| 400 | 建筑经纬度不能为空 | 创建时缺少坐标信息 |
| 400 | 建筑经度范围应在-180到180之间 | 经度坐标超出有效范围 |
| 400 | 建筑纬度范围应在-90到90之间 | 纬度坐标超出有效范围 |
| 400 | 建造时间不能晚于当前时间 | 建造时间设置为未来时间 |
| 400 | 历史要素不存在 | 操作的历史要素ID不存在 |
| 400 | 历史要素编号已存在 | 创建或更新时编号重复 |
| 401 | 未提供认证令牌 | 缺少Authorization头 |
| 401 | 认证令牌无效或已过期 | Token无效 |
| 403 | 权限不足，需要管理员权限 | 非admin角色访问管理接口 |
| 422 | 参数校验错误 | 请求参数不符合验证规则 |

---

## 注意事项

1. **权限控制**:
   - 所有历史要素管理接口都需要管理员权限
   - 只有admin角色可以访问这些接口
   - 公开接口可供前端展示使用

2. **数据验证**:
   - 历史要素名称不能为空
   - 建筑坐标必须在有效范围内
   - 建造时间不能晚于当前时间
   - 编号在系统内必须唯一

3. **坐标系统**:
   - 使用WGS84坐标系统
   - 经度范围：-180到180
   - 纬度范围：-90到90

4. **时间格式**:
   - 建造时间使用ISO 8601格式
   - 支持历史时间（如公元652年）
   - 时间轴按年份分组展示

5. **关联关系**:
   - 可关联到类型字典项进行分类
   - 必须关联到已存在的区域字典项
   - 支持关联多张照片

6. **批量操作**:
   - 支持批量导入历史要素
   - 批量导入时会逐一验证数据
   - 任一数据验证失败会终止整个导入

7. **统计功能**:
   - 支持按类型、区域、时期统计
   - 时期按中国历史朝代划分
   - 统计数据实时计算

8. **删除影响**:
   - 默认删除历史要素时保留关联照片（外键设置为NULL）
   - 可通过deletePhotos=true参数同时删除关联照片和物理文件
   - 删除操作不可恢复，请谨慎操作
   - 建议在删除前备份重要数据

9. **搜索功能**:
   - 支持按名称模糊搜索
   - 支持按类型和区域筛选
   - 支持按建造时间范围查询

10. **缓存机制**:
    - 统计数据会进行缓存优化
    - 数据变更时自动刷新相关缓存
