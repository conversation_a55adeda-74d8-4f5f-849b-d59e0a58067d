import { Rule, RuleType } from '@midwayjs/validate';
import { PageQueryDTO } from './common.dto';

// ==================== 区域字典 DTO ====================

/**
 * 创建区域字典DTO
 */
export class CreateRegionDictDTO {
  @Rule(RuleType.string().required().max(50))
  regionCode: string;

  @Rule(RuleType.string().required().max(255))
  regionName: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().valid(0, 1).default(1))
  status?: number = 1;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  regionDesc?: string;
}

/**
 * 更新区域字典DTO
 */
export class UpdateRegionDictDTO {
  @Rule(RuleType.string().optional().allow(null).max(50))
  regionCode?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  regionName?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().valid(0, 1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  regionDesc?: string;
}

/**
 * 区域字典查询DTO
 */
export class RegionDictQueryDTO extends PageQueryDTO {
  @Rule(RuleType.string().optional().allow('').allow(null))
  keyword?: string;

  @Rule(RuleType.number().integer().valid(0, 1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;
}

// ==================== 类型字典 DTO ====================

/**
 * 创建类型字典DTO
 */
export class CreateTypeDictDTO {
  @Rule(RuleType.string().required().max(50))
  typeCode: string;

  @Rule(RuleType.string().required().max(255))
  typeName: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().valid(0, 1).default(1))
  status?: number = 1;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  typeDesc?: string;
}

/**
 * 更新类型字典DTO
 */
export class UpdateTypeDictDTO {
  @Rule(RuleType.string().optional().allow(null).max(50))
  typeCode?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  typeName?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().valid(0, 1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  typeDesc?: string;
}

/**
 * 类型字典查询DTO
 */
export class TypeDictQueryDTO extends PageQueryDTO {
  @Rule(RuleType.string().optional().allow('').allow(null))
  keyword?: string;

  @Rule(RuleType.number().integer().valid(0, 1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;
}

// ==================== 关系字典 DTO ====================

/**
 * 创建关系字典DTO
 */
export class CreateRelationshipDictDTO {
  @Rule(RuleType.string().required().max(50))
  relationCode: string;

  @Rule(RuleType.string().required().max(255))
  relationName: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().valid(0, 1).default(1))
  status?: number = 1;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  relationDesc?: string;
}

/**
 * 更新关系字典DTO
 */
export class UpdateRelationshipDictDTO {
  @Rule(RuleType.string().optional().allow(null).max(50))
  relationCode?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  relationName?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;

  @Rule(RuleType.number().integer().valid(0, 1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  sort?: number;

  @Rule(RuleType.string().optional().allow(null))
  relationDesc?: string;
}

/**
 * 关系字典查询DTO
 */
export class RelationshipDictQueryDTO extends PageQueryDTO {
  @Rule(RuleType.string().optional().allow('').allow(null))
  keyword?: string;

  @Rule(RuleType.number().integer().valid(0, 1).optional().allow(null))
  status?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  parentId?: number;
}

// ==================== 批量操作 DTO ====================

/**
 * 批量更新状态DTO
 */
export class BatchUpdateStatusDTO {
  @Rule(RuleType.array().items(RuleType.number().integer()).required())
  ids: number[];

  @Rule(RuleType.number().integer().valid(0, 1).required())
  status: number;
}

// ==================== 响应 DTO ====================

/**
 * 字典响应DTO基类
 */
export class DictResponseDTO {
  id: number;
  status: number;
  sort?: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 区域字典响应DTO
 */
export class RegionDictResponseDTO extends DictResponseDTO {
  regionCode: string;
  regionName: string;
  parentId?: number;
  regionDesc?: string | null;
  parent?: RegionDictResponseDTO;
  children?: RegionDictResponseDTO[];

  constructor(data: any) {
    super();
    Object.assign(this, data);
  }
}

/**
 * 类型字典响应DTO
 */
export class TypeDictResponseDTO extends DictResponseDTO {
  typeCode: string;
  typeName: string;
  parentId?: number;
  typeDesc?: string;
  parent?: TypeDictResponseDTO;
  children?: TypeDictResponseDTO[];

  constructor(data: any) {
    super();
    Object.assign(this, data);
  }
}

/**
 * 关系字典响应DTO
 */
export class RelationshipDictResponseDTO extends DictResponseDTO {
  relationCode: string;
  relationName: string;
  parentId?: number;
  relationDesc?: string;
  parent?: RelationshipDictResponseDTO;
  children?: RelationshipDictResponseDTO[];

  constructor(data: any) {
    super();
    Object.assign(this, data);
  }
}
