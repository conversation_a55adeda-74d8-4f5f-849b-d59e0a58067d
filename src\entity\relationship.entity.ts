import {
  Table,
  Column,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { RelationshipDict } from './relationship-dict.entity';

export interface RelationshipAttributes {
  /** ID */
  id: number;
  /** 关系类型ID */
  relationDictId?: number;
  /** 父级关系ID */
  parentRelationshipId?: number;
  /** 源要素类型 (mountain, water_system, historical_element) */
  sourceType: string;
  /** 关联源ID（如山塬ID、水系ID、历史要素ID） */
  sourceId: number;
  /** 目标类型 (element, category) */
  targetType: string;
  /** 目标要素类型 (mountain, water_system, historical_element, type_dict, region_dict) */
  targetEntityType?: string;
  /** 关联目标ID（如山塬ID、水系ID、历史要素ID） */
  targetId: number;
  /** 关联方向 (前有, 后有, 上有, 下有, 东连, 西连, 南为, 北为, 等) */
  direction?: string;
  /** 词条描述 */
  term?: string;
  /** 记载内容 */
  record?: string;
  /** 排序号 */
  sort?: number;
  /** 状态 (1启用, 0禁用) */
  status?: number;
}

/**
 * 关系表模型
 */
@Table({
  tableName: 'relationship',
  comment: '关系表',
  indexes: [
    {
      fields: ['source_type', 'source_id'],
      name: 'idx_source',
    },
    {
      fields: ['target_type', 'target_entity_type', 'target_id'],
      name: 'idx_target',
    },
    {
      fields: ['relation_dict_id'],
      name: 'idx_relation_dict',
    },
  ],
})
export class Relationship
  extends Model<RelationshipAttributes>
  implements RelationshipAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => RelationshipDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '关系类型ID',
    field: 'relation_dict_id',
  })
  relationDictId: number;

  @ForeignKey(() => Relationship)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '父级关系ID',
    field: 'parent_relationship_id',
  })
  parentRelationshipId: number;

  @Column({
    type: DataType.ENUM('mountain', 'water_system', 'historical_element'),
    allowNull: false,
    comment: '源要素类型',
    field: 'source_type',
  })
  sourceType: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联源ID（如山塬ID、水系ID、历史要素ID）',
    field: 'source_id',
  })
  sourceId: number;

  @Column({
    type: DataType.ENUM('element', 'category'),
    allowNull: false,
    comment: '目标类型 (element=具体要素, category=类别)',
    field: 'target_type',
  })
  targetType: string;

  @Column({
    type: DataType.ENUM(
      'mountain',
      'water_system',
      'historical_element',
      'type_dict',
      'region_dict'
    ),
    allowNull: true,
    comment: '目标要素类型',
    field: 'target_entity_type',
  })
  targetEntityType: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联目标ID（如山塬ID、水系ID、历史要素ID）',
    field: 'target_id',
  })
  targetId: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '关联方向',
  })
  direction: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '词条描述',
  })
  term: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '记载内容',
  })
  record: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '排序号',
  })
  sort: number;

  @Column({
    type: DataType.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1启用，0禁用）',
  })
  status: number;

  // 关联关系
  @BelongsTo(() => RelationshipDict, 'relationDictId')
  relationDict: RelationshipDict;

  // 自关联关系
  @BelongsTo(() => Relationship, 'parentRelationshipId')
  parent: Relationship;

  @HasMany(() => Relationship, 'parentRelationshipId')
  children: Relationship[];
}
