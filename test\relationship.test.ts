import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/relationship.test.ts', () => {
  let app: Application;
  let token: string;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
      
      // 登录获取token
      const loginResult = await createHttpRequest(app)
        .post('/admin/auth/login')
        .send({
          username: 'admin',
          password: 'admin123',
        });

      expect(loginResult.status).toBe(200);
      expect(loginResult.body.errCode).toBe(0);
      token = loginResult.body.data.token;
    } catch (err) {
      console.error('setup error', err);
      throw err;
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should create relationship', async () => {
    const result = await createHttpRequest(app)
      .post('/admin/relationship')
      .set('Authorization', `Bearer ${token}`)
      .send({
        sourceType: 'mountain',
        sourceId: 1,
        targetType: 'element',
        targetEntityType: 'water_system',
        targetId: 1,
        relationDictId: 1,
        direction: '前有',
        term: '前有',
        record: '测试记载内容',
        sort: 1,
        status: 1,
      });

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data.sourceType).toBe('mountain');
  });

  it('should get relationship list', async () => {
    const result = await createHttpRequest(app)
      .get('/admin/relationship')
      .set('Authorization', `Bearer ${token}`)
      .query({
        page: 1,
        pageSize: 10,
      });

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('data');
    expect(result.body.data).toHaveProperty('total');
    expect(result.body.data).toHaveProperty('page');
    expect(result.body.data).toHaveProperty('pageSize');
  });

  it('should get network graph data', async () => {
    const result = await createHttpRequest(app)
      .get('/admin/relationship/network-graph')
      .set('Authorization', `Bearer ${token}`);

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('nodes');
    expect(result.body.data).toHaveProperty('links');
    expect(result.body.data).toHaveProperty('categories');
    expect(Array.isArray(result.body.data.nodes)).toBe(true);
    expect(Array.isArray(result.body.data.links)).toBe(true);
    expect(Array.isArray(result.body.data.categories)).toBe(true);
  });

  it('should get relationship statistics', async () => {
    const result = await createHttpRequest(app)
      .get('/admin/relationship/statistics/overview')
      .set('Authorization', `Bearer ${token}`);

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('total');
    expect(result.body.data).toHaveProperty('bySourceType');
    expect(result.body.data).toHaveProperty('byTargetType');
    expect(result.body.data).toHaveProperty('byRelationType');
    expect(result.body.data).toHaveProperty('byDirection');
  });

  it('should get public network graph data', async () => {
    const result = await createHttpRequest(app)
      .get('/public/relationship/network-graph');

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('nodes');
    expect(result.body.data).toHaveProperty('links');
    expect(result.body.data).toHaveProperty('categories');
  });

  it('should search relationships', async () => {
    const result = await createHttpRequest(app)
      .get('/public/relationship/search')
      .query({
        keyword: '测试',
      });

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('data');
    expect(result.body.data).toHaveProperty('total');
  });

  it('should batch create relationships', async () => {
    const result = await createHttpRequest(app)
      .post('/admin/relationship/batch')
      .set('Authorization', `Bearer ${token}`)
      .send({
        relations: [
          {
            sourceType: 'mountain',
            sourceId: 1,
            targetType: 'element',
            targetEntityType: 'historical_element',
            targetId: 1,
            relationDictId: 1,
            direction: '后有',
            term: '后有',
            record: '批量测试记载1',
          },
          {
            sourceType: 'water_system',
            sourceId: 1,
            targetType: 'element',
            targetEntityType: 'mountain',
            targetId: 1,
            relationDictId: 1,
            direction: '绕其后',
            term: '绕其后',
            record: '批量测试记载2',
          },
        ],
      });

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data.message).toBe('批量创建成功');
  });

  it('should update relationship', async () => {
    // 先创建一个关联
    const createResult = await createHttpRequest(app)
      .post('/admin/relationship')
      .set('Authorization', `Bearer ${token}`)
      .send({
        sourceType: 'mountain',
        sourceId: 1,
        targetType: 'element',
        targetEntityType: 'water_system',
        targetId: 1,
        relationDictId: 1,
        direction: '前有',
        term: '前有',
        record: '原始记载',
      });

    const relationId = createResult.body.data.id;

    // 更新关联
    const updateResult = await createHttpRequest(app)
      .put(`/admin/relationship/${relationId}`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        direction: '后有',
        term: '后有',
        record: '更新后的记载',
      });

    expect(updateResult.status).toBe(200);
    expect(updateResult.body.errCode).toBe(0);
    expect(updateResult.body.data.direction).toBe('后有');
    expect(updateResult.body.data.term).toBe('后有');
    expect(updateResult.body.data.record).toBe('更新后的记载');
  });

  it('should delete relationship', async () => {
    // 先创建一个关联
    const createResult = await createHttpRequest(app)
      .post('/admin/relationship')
      .set('Authorization', `Bearer ${token}`)
      .send({
        sourceType: 'mountain',
        sourceId: 1,
        targetType: 'element',
        targetEntityType: 'water_system',
        targetId: 1,
        relationDictId: 1,
        direction: '前有',
        term: '前有',
        record: '待删除记载',
      });

    const relationId = createResult.body.data.id;

    // 删除关联
    const deleteResult = await createHttpRequest(app)
      .delete(`/admin/relationship/${relationId}`)
      .set('Authorization', `Bearer ${token}`);

    expect(deleteResult.status).toBe(200);
    expect(deleteResult.body.errCode).toBe(0);
    expect(deleteResult.body.data.message).toBe('删除成功');
  });
});
